defmodule Drops.Operations do
  @moduledoc """
  Operations module for defining command and query operations.

  This module provides a framework for defining operations that can be used
  to encapsulate business logic with input validation and execution.

  ## Extension Registration

  Extensions can be registered using the `register_extension` macro:

      defmodule MyApp.Operations do
        use Drops.Operations

        register_extension(MyApp.Extensions.Audit)
      end

  """

  require Drops.Operations.Extension

  alias Drops.Operations.{Extension, UnitOfWork}

  # Default options for all operations
  @default_opts [extensions: [Drops.Operations.Extensions.Ecto]]

  defmodule Success do
    @type t :: %__MODULE__{}

    defstruct [:type, :operation, :result, :params]
  end

  defmodule Failure do
    @type t :: %__MODULE__{}

    defstruct [:type, :operation, :result, :params]
  end

  @doc """
  Callback for executing an operation with given context.
  The context is a map that contains at least a :params key.
  """
  @callback execute(context :: map()) :: {:ok, any()} | {:error, any()}

  @doc """
  Callback for preparing parameters before execution.
  Receives context map and should return updated context.
  """
  @callback prepare(context :: map()) :: {:ok, map()} | {:error, any()}

  @doc """
  Callback for validating parameters.
  Receives context map and should return validated context or error.
  """
  @callback validate(context :: map()) :: {:ok, map()} | {:error, any()}

  @optional_callbacks prepare: 1, validate: 1

  @doc """
  Before compile callback to extend UoW after all schema macros have been processed.
  """
  defmacro __before_compile__(env) do
    module = env.module

    opts = Module.get_attribute(module, :opts)
    base_module = Module.get_attribute(module, :base_module)
    schema_meta = Module.get_attribute(module, :schema_meta, %{})

    final_opts = Keyword.put(opts, :schema_meta, schema_meta)
    base_steps = [:conform, :prepare, :validate, :execute]

    steps =
      if has_empty_schema?(module) do
        List.delete(base_steps, :conform)
      else
        base_steps
      end

    # Get registered extensions from opts
    registered_extensions = Keyword.get(opts, :extensions, [])

    # Generate extension code (this will filter to enabled extensions internally)
    {extension_code, _extension_function_names} =
      Extension.extend_operation(registered_extensions, final_opts)

    enabled_extensions = Extension.enabled_extensions(registered_extensions, final_opts)

    base_unit_of_work = UnitOfWork.new(module, steps)

    unit_of_work =
      Drops.Operations.Extension.extend_unit_of_work(
        base_unit_of_work,
        module,
        enabled_extensions,
        final_opts
      )

    Module.put_attribute(module, :unit_of_work, unit_of_work)
    Module.put_attribute(module, :enabled_extensions, enabled_extensions)

    # Add all parent extensions to this module's registered extensions
    if base_module do
      for extension <- registered_extensions do
        Module.put_attribute(module, :registered_extensions, extension)
      end
    end

    quote location: :keep do
      def registered_extensions do
        # Extensions are stored in @opts during compilation
        Keyword.get(@opts, :extensions, [])
      end

      def enabled_extensions, do: @enabled_extensions

      def __unit_of_work__, do: @unit_of_work

      # Inject extension code AFTER imports so extensions can override
      unquote_splicing(extension_code)

      # Inject steps and helpers from enabled extensions
      unquote(
        enabled_extensions
        |> Enum.map(fn extension ->
          helpers =
            if function_exported?(extension, :helpers, 0) do
              IO.puts("Helpers: #{inspect(extension)} -> #{inspect(extension.helpers())}")
              extension.helpers()
            else
              []
            end

          quote location: :keep do
            unquote(helpers)
          end
        end)
      )

      # Inject steps and helpers from enabled extensions
      unquote(
        enabled_extensions
        |> Enum.map(fn extension ->
          steps =
            if function_exported?(extension, :steps, 0) do
              extension.steps()
            else
              []
            end

          quote location: :keep do
            unquote(steps)
          end
        end)
      )

      def prepare(context) do
        {:ok, context}
      end

      def validate(context) do
        {:ok, context}
      end

      def execute(_context) do
        raise "#{__MODULE__}.execute/1 must be implemented"
      end
    end
  end

  @doc """
  Register an extension module for this operations module.

  This macro accumulates extension modules in the `:registered_extensions` module attribute.
  When operations are defined using this module as a base, they will automatically
  be extended with the registered extensions.

  ## Parameters

  - `extension` - The extension module to register

  ## Example

      defmodule MyApp.Operations do
        use Drops.Operations

        register_extension(MyApp.Extensions.Audit)
        register_extension(MyApp.Extensions.Cache)
      end
  """

  defmacro steps(do: block) do
    quote do
      @steps unquote(Macro.escape(block))

      def steps, do: @steps
    end
  end

  @doc """
  Merge options with parent options, handling extension inheritance properly.
  """
  def merge_opts(parent_opts, new_opts) do
    parent_extensions = Keyword.get(parent_opts, :extensions, [])
    new_extensions = Keyword.get(new_opts, :extensions, [])

    # Merge basic options
    merged_opts = Keyword.merge(parent_opts, new_opts)

    # Combine extensions from parent and new opts
    all_extensions = (parent_extensions ++ new_extensions) |> Enum.uniq()

    merged_opts
    |> Keyword.put(:extensions, all_extensions)
  end

  defmacro __using__(opts) do
    # Merge with default opts
    merged_opts = Keyword.merge(@default_opts, opts)

    quote location: :keep do
      import Drops.Operations

      @opts unquote(merged_opts)
      def __opts__, do: @opts

      def registered_extensions do
        Keyword.get(@opts, :extensions, [])
      end

      require Drops.Operations.Extensions.Ecto

      defmacro __using__(opts) when opts == [] do
        # Use __CALLER__.module as target and __MODULE__ as parent
        Drops.Operations.__define_operation__(@opts, __MODULE__, __CALLER__.module)
      end

      defmacro __using__(type) when is_atom(type) do
        merged_opts = Drops.Operations.merge_opts(@opts, type: type)
        Drops.Operations.__define_operation__(merged_opts, __MODULE__, __CALLER__.module)
      end

      defmacro __using__(opts) when is_list(opts) do
        merged_opts = Drops.Operations.merge_opts(@opts, opts)
        Drops.Operations.__define_operation__(merged_opts, __MODULE__, __CALLER__.module)
      end
    end
  end

  def __define_operation__(opts, parent_module, _target_module) do
    # Get parent opts if available
    base_opts =
      if parent_module && parent_module != Drops.Operations,
        do: parent_module.__opts__(),
        else: @default_opts

    # Merge opts properly using centralized logic
    merged_opts = merge_opts(base_opts, opts)

    # Get all registered extensions
    all_extensions = Keyword.get(merged_opts, :extensions, [])

    # Merge schema options from extensions and user input
    schema_opts = Extension.merge_schema_opts(all_extensions, merged_opts)
    final_opts = Keyword.put(merged_opts, :schema, schema_opts)

    # We'll defer extension resolution to __before_compile__ to ensure parent module is fully compiled
    steps_code =
      if parent_module && function_exported?(parent_module, :steps, 0),
        do: parent_module.steps(),
        else: []

    # Calculate extension function names that will be defined in this module
    registered_extensions = Keyword.get(final_opts, :extensions, [])

    {_extension_code, extension_function_names_list} =
      Extension.extend_operation(registered_extensions, final_opts)

    extension_function_names = List.flatten(extension_function_names_list)

    # Calculate parent imports with extension function names to exclude conflicts
    parent_functions =
      if parent_module do
        __MODULE__.calculate_parent_imports(parent_module, extension_function_names)
      else
        []
      end

    quote location: :keep do
      import Drops.Operations

      @behaviour Drops.Operations

      use Drops.Contract

      # Import functions from parent module (excluding extension functions to avoid conflicts)
      unquote(
        if parent_functions != [] && parent_module do
          quote do
            import unquote(parent_module), only: unquote(parent_functions)
          end
        else
          quote do
          end
        end
      )

      schema do
        %{}
      end

      def conform(%{params: params} = context) when is_map(context) do
        case super(params) do
          {:ok, conformed_params} ->
            {:ok, Map.put(context, :params, conformed_params)}

          {:error, _} = error ->
            error
        end
      end

      require unquote(parent_module)
      import unquote(parent_module), only: unquote(parent_functions)

      # Store base module for runtime extension inheritance
      # Only store if it's not the root Drops.Operations module
      @base_module unquote(
                     if parent_module != Drops.Operations, do: parent_module, else: nil
                   )
      @operation_type unquote(opts[:type])
      @opts unquote(final_opts)
      @schema_opts unquote(schema_opts)

      @before_compile Drops.Operations

      defmacro __using__(opts) when opts == [] do
        Drops.Operations.__define_operation__(@opts, __MODULE__, __CALLER__.module)
      end

      defmacro __using__(type) when is_atom(type) do
        merged_opts = Drops.Operations.merge_opts(@opts, type: type)
        Drops.Operations.__define_operation__(merged_opts, __MODULE__, __CALLER__.module)
      end

      defmacro __using__(opts) when is_list(opts) do
        merged_opts = Drops.Operations.merge_opts(@opts, opts)
        Drops.Operations.__define_operation__(merged_opts, __MODULE__, __CALLER__.module)
      end

      def __opts__, do: @opts
      def __operation_type__, do: @operation_type

      def call(context) do
        UnitOfWork.process(__unit_of_work__(), context)
      end

      def call({:ok, previous_result}, context) do
        UnitOfWork.process(
          __unit_of_work__(),
          Map.put(context, :execute_result, previous_result)
        )
      end

      def call({:error, _error} = error_result, _input) do
        error_result
      end

      unquote(steps_code)
    end
  end

  defp has_empty_schema?(operation_module) do
    schemas = Module.get_attribute(operation_module, :schemas, %{})
    default_schema = schemas[:default]

    default_schema == nil or length(default_schema.keys) == 0
  end

  # Calculate which functions should be imported from the parent module.
  # Excludes functions that come from:
  # - Enabled extensions
  # - Operations framework itself
  # - Drops.Contract
  def calculate_parent_imports(mod, extension_function_names) do
    # Get all functions from parent module
    parent_functions = mod.__info__(:functions)

    # Functions provided by Operations framework
    operations_functions = [
      {:schema, 0},
      {:schema, 1},
      {:schemas, 0},
      {:conform, 1},
      {:call, 1},
      {:call, 2},
      {:registered_extensions, 0},
      {:enabled_extensions, 0},
      {:prepare, 1},
      {:validate, 1},
      {:execute, 1},
      {:__unit_of_work__, 0},
      {:__opts__, 0},
      {:__operation_type__, 0}
    ]

    contract_functions = Drops.Contract.__info__(:functions)

    # Functions to exclude - use the extension function names passed in
    excluded_functions =
      operations_functions ++ contract_functions ++ extension_function_names

    # Filter parent functions to only include user-defined ones
    functions_to_import =
      parent_functions
      |> Enum.reject(fn func -> func in excluded_functions end)

    functions_to_import
  end
end
